#[glue::contract]
mod explorer_db {
    use serde::{Deserialize, Serialize};

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "u8", from = "u8")]
    pub enum OperationType {
        CreateContract,
        CallContract,
        ForkContract,
        UpgradeContract,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "bool", from = "bool")]
    pub enum TransactionExecutionStatus {
        Failure,
        Success,
    }

    impl From<TransactionExecutionStatus> for bool {
        fn from(status: TransactionExecutionStatus) -> bool {
            match status {
                TransactionExecutionStatus::Failure => false,
                TransactionExecutionStatus::Success => true,
            }
        }
    }

    impl From<bool> for TransactionExecutionStatus {
        fn from(value: bool) -> Self {
            match value {
                false => TransactionExecutionStatus::Failure,
                true => TransactionExecutionStatus::Success,
            }
        }
    }

    impl From<OperationType> for u8 {
        fn from(op_type: OperationType) -> u8 {
            match op_type {
                OperationType::CreateContract => 0,
                OperationType::CallContract => 1,
                OperationType::ForkContract => 2,
                OperationType::UpgradeContract => 3,
            }
        }
    }

    impl From<u8> for OperationType {
        fn from(value: u8) -> Self {
            match value {
                0 => OperationType::CreateContract,
                1 => OperationType::CallContract,
                2 => OperationType::ForkContract,
                3 => OperationType::UpgradeContract,
                _ => panic!("Invalid operation type value"),
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CreateContractOperation {
        pub op_type: OperationType,
        pub contract_hex_bytecode: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CallContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub function_name: String,
        pub parameters: Vec<serde_json::Value>,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct ForkContractOperation {
        pub op_type: OperationType,
        pub contract_code_hash: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct UpgradeContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub contract_hex_bytecode: String,
        pub contract_source_url: String,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationData {
        CreateContract(CreateContractOperation),
        CallContract(CallContractOperation),
        ForkContract(ForkContractOperation),
        UpgradeContract(UpgradeContractOperation),
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCreateContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCallContractResult {
        pub op_type: OperationType,
        pub return_data: serde_json::Value, // Data returned from the contract call
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationForkContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationUpgradeContractResult {
        pub op_type: OperationType,
        pub code_hash: String, // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationResult {
        CreateContract(OperationCreateContractResult),
        CallContract(OperationCallContractResult),
        ForkContract(OperationForkContractResult),
        UpgradeContract(OperationUpgradeContractResult),
    }

    impl Default for OperationResult {
        fn default() -> Self {
            // Default to a simple call contract operation result
            OperationResult::CallContract(OperationCallContractResult {
                op_type: OperationType::CallContract,
                return_data: serde_json::Value::Null,
            })
        }
    }

    impl Default for OperationData {
        fn default() -> Self {
            // Default to a simple call contract operation
            OperationData::CallContract(CallContractOperation {
                op_type: OperationType::CallContract,
                contract_address: String::new(),
                function_name: String::new(),
                parameters: Vec::new(),
            })
        }
    }

    #[glue::storage_item]
    pub struct Transaction {
        pub hash: String,
        pub sender: String,
        pub timestamp: u64,
        pub dependent_transaction_hash: String,
        pub op_data: OperationData,
        pub fuel: u64,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,

        // Receipt fields
        pub transaction_index: u64,
        pub status: bool,
        pub op_result: OperationResult, // Operation result
        pub block_hash: String,
    }

    #[glue::storage_item]
    pub struct Block {
        pub hash: String,
        pub parent_hash: String,
        pub height: u64,
        pub state_root: String,
        pub transactions_root: String,
        pub receipts_root: String,
        pub local_timestamp: u64,
        pub protocol_timestamp: u64,
        pub slot_id: u8,
        pub proposer_address: String,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,
        pub transactions: Vec<Transaction>,
    }

    #[glue::storage]
    pub struct ExplorerDB {
        pub transactions: glue::collections::Map<String, Transaction>,
        pub blocks: glue::collections::Map<String, Block>,
        pub header_chain: glue::collections::Vec<String>, // record block hash
    }

    impl ExplorerDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            self.transactions.bind_index(
                "address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    match &v.op_data {
                        OperationData::CallContract(op) => match op.function_name.as_str() {
                            "transfer" => {
                                vec![
                                    format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp),
                                    format!("{}-{:0>19}", op.parameters[1].as_str().unwrap(), v.timestamp),
                                ]
                            }
                            "issue" => {
                                vec![format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp)]
                            }
                            "mint_token" => {
                                vec![format!("{}-{:0>19}", op.parameters[0].as_str().unwrap(), v.timestamp)]
                            }
                            _ => Vec::new(),
                        },
                        _ => Vec::new(),
                    }
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                transactions: glue::collections::Map::new(),
                blocks: glue::collections::Map::new(),
                header_chain: glue::collections::Vec::new(),
            };
            ret.bind_index();
            ret
        }

        #[glue::atomic]
        pub fn insert_block(&mut self, block_json: String) -> anyhow::Result<()> {
            let block: Block = serde_json::from_str(&block_json)?;

            self.blocks.insert(&block.hash, &block);

            // Insert all transactions from the block
            for transaction in &block.transactions {
                self.insert_transaction(transaction)?;
            }

            // Check height and parent hash
            if self.header_chain.len() > 0 {
                let last_block_hash = self.header_chain.last().unwrap();
                let last_block = self.blocks.get(&last_block_hash).unwrap();
                if block.height != last_block.height + 1 {
                    return Err(anyhow::anyhow!(
                        "Block height is not continuous, expected height {}, got height {}",
                        last_block.height + 1,
                        block.height
                    ));
                }
                if block.parent_hash != *last_block_hash {
                    return Err(anyhow::anyhow!(
                        "Block parent hash does not match, expected {}, got {}, may be a fork or reorg",
                        last_block_hash,
                        block.parent_hash
                    ));
                }
            }

            self.header_chain.push(&block.hash);

            Ok(())
        }

        fn insert_transaction(&mut self, transaction: &Transaction) -> anyhow::Result<()> {
            // Insert the transaction with its hash as the key
            self.transactions.insert(&transaction.hash, &transaction);
            Ok(())
        }

        #[glue::readonly]
        pub fn get_address_transactions(
            &self,
            reverse: bool,
            address: String,
            start: u64,
            end: u64,
            limit: u64,
        ) -> anyhow::Result<String> {
            let mut count = 0u64;
            let (start, end) = if !reverse {
                (
                    format!("{}-{:0>19}", address, start),
                    format!("{}-{:9>19}", address, end),
                )
            } else {
                (
                    format!("{}-{:9>19}", address, start),
                    format!("{}-{:0>19}", address, end),
                )
            };

            let mut transactions = vec![];
            let mut iter = self
                .transactions
                .index("address")
                .iter(reverse, &start, &end);

            while iter.next() {
                println!("key: {}, value: {:?}", iter.key()?, iter.value()?);
                transactions.push(iter.value()?);
                count += 1;
                if count >= limit {
                    break;
                }
            }
            Ok(serde_json::to_string(&transactions)?)
        }

        #[glue::readonly]
        pub fn get_block_by_hash(&self, block_hash: String) -> anyhow::Result<String> {
            let block = self.blocks.get(&block_hash);
            match block {
                Some(block) => Ok(serde_json::to_string(&block)?),
                None => Err(anyhow::anyhow!("Block not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_by_hash(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_data(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction.op_data)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_result(
            &self,
            transaction_hash: String,
        ) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => {
                    // Directly serialize the operation result
                    Ok(serde_json::to_string(&transaction.op_result)?)
                }
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_synced_block_height(&self) -> anyhow::Result<u64> {
            if self.header_chain.len() == 0 {
                return Err(anyhow::anyhow!("No blocks found"));
            }
            Ok((self.header_chain.len() - 1) as u64)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[glue::test]
    fn test_get_address_transactions() {
        // 创建一个新的 ExplorerDB 实例
        explorer_db::set_instance(explorer_db::ExplorerDB::new());
        let mut db = explorer_db::get_instance();
        
        // 创建测试交易
        let create_test_transaction = |hash: &str, sender: &str, timestamp: u64, address1: &str, address2: Option<&str>| {
            let op_data = if let Some(addr2) = address2 {
                // 创建一个 transfer 操作的交易
                OperationData::CallContract(CallContractOperation {
                    op_type: OperationType::CallContract,
                    contract_address: "0xcontract".to_string(),
                    function_name: "transfer".to_string(),
                    parameters: vec![
                        serde_json::Value::String(address1.to_string()),
                        serde_json::Value::String(addr2.to_string()),
                    ],
                })
            } else {
                // 创建一个 issue 操作的交易
                OperationData::CallContract(CallContractOperation {
                    op_type: OperationType::CallContract,
                    contract_address: "0xcontract".to_string(),
                    function_name: "issue".to_string(),
                    parameters: vec![
                        serde_json::Value::String(address1.to_string()),
                        serde_json::Value::Number(serde_json::Number::from(100)),
                    ],
                })
            };
            
            Transaction {
                hash: hash.to_string(),
                sender: sender.to_string(),
                timestamp,
                dependent_transaction_hash: "".to_string(),
                op_data,
                fuel: 100,
                public_keys: vec![],
                signatures: vec![],
                transaction_index: 0,
                status: true,
                op_result: OperationResult::default(),
                block_hash: "0xblock".to_string(),
            }
        };
        
        // 创建测试区块
        let create_test_block = |hash: &str, height: u64, transactions: Vec<Transaction>| {
            Block {
                hash: hash.to_string(),
                parent_hash: if height > 1 { format!("0xblock{}", height - 1) } else { "0x0".to_string() },
                height,
                state_root: "0xstate".to_string(),
                transactions_root: "0xtx".to_string(),
                receipts_root: "0xreceipts".to_string(),
                local_timestamp: 1000000 + height * 1000,
                protocol_timestamp: 1000000 + height * 1000,
                slot_id: 0,
                proposer_address: "0xproposer".to_string(),
                public_keys: vec![],
                signatures: vec![],
                transactions,
            }
        };
        
        // 创建测试数据
        let address1 = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a";
        let address2 = "0xabc8b8fc2831c0b13286ce322dd8ca0d2b2f643a";
        
        // 创建不同时间戳的交易
        let tx1 = create_test_transaction("0xtx1", "0xsender", 1000000000, address1, None);
        let tx2 = create_test_transaction("0xtx2", "0xsender", 2000000000, address1, Some(address2));
        let tx3 = create_test_transaction("0xtx3", "0xsender", 3000000000, address2, Some(address1));
        let tx4 = create_test_transaction("0xtx4", "0xsender", 4000000000, address1, None);
        let tx5 = create_test_transaction("0xtx5", "0xsender", 5000000000, address2, Some(address1));
        
        // 创建区块并插入
        let block1 = create_test_block("0xblock1", 1, vec![tx1.clone(), tx2.clone()]);
        let block2 = create_test_block("0xblock2", 2, vec![tx3.clone(), tx4.clone(), tx5.clone()]);
        
        // 将区块序列化为 JSON 并插入
        let block1_json = serde_json::to_string(&block1).unwrap();
        let block2_json = serde_json::to_string(&block2).unwrap();
        
        // 确保绑定索引
        db.bind_index();
        
        // 插入区块
        db.insert_block(block1_json).unwrap();
        db.insert_block(block2_json).unwrap();
        
        // 测试正向迭代 (从早到晚)
        let result = db.get_address_transactions(false, address1.to_string(), 0, 9999999999, 10).unwrap();
        let transactions: Vec<Transaction> = serde_json::from_str(&result).unwrap();
        
        // 验证结果 - 应该找到与address1相关的交易
        assert!(transactions.len() > 0, "应该找到与address1相关的交易");
        
        // 测试反向迭代 (从晚到早)
        let result = db.get_address_transactions(true, address1.to_string(), 9999999999, 0, 10).unwrap();
        let transactions: Vec<Transaction> = serde_json::from_str(&result).unwrap();
        
        // 验证结果 - 应该找到与address1相关的交易
        assert!(transactions.len() > 0, "应该找到与address1相关的交易");
        
        // 测试时间范围限制
        let result = db.get_address_transactions(false, address1.to_string(), 1500000000, 4500000000, 10).unwrap();
        let transactions: Vec<Transaction> = serde_json::from_str(&result).unwrap();
        
        // 验证结果 - 应该找到在时间范围内的交易
        assert!(transactions.len() > 0, "应该找到在时间范围内的交易");
        
        // 测试限制数量
        let result = db.get_address_transactions(false, address1.to_string(), 0, 9999999999, 1).unwrap();
        let transactions: Vec<Transaction> = serde_json::from_str(&result).unwrap();
        
        // 验证结果 - 应该只返回指定数量的交易
        assert!(transactions.len() <= 1, "应该只返回指定数量的交易");
        
        // 测试不存在的地址
        let result = db.get_address_transactions(false, "0xnonexistent".to_string(), 0, 9999999999, 10).unwrap();
        let transactions: Vec<Transaction> = serde_json::from_str(&result).unwrap();
        
        // 验证结果 - 不存在的地址应该返回空列表
        assert_eq!(transactions.len(), 0, "不存在的地址应该返回空列表");
    }
}
